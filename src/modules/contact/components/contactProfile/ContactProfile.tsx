import { Field, FieldArray, Form, Formik } from 'formik'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { Link, useNavigate, useParams } from 'react-router-dom'
import * as Yup from 'yup'
import AutoCompleteAddress from '../../../../shared/autoCompleteAdress/AutoCompleteAddress'
import Button from '../../../../shared/components/button/Button'
import { CustomModal } from '../../../../shared/customModal/CustomModal'
import CustomSelect from '../../../../shared/customSelect/CustomSelect'
import { onlyNumber, onlyText } from '../../../../shared/helpers/regex'
import {
  countObjectDifferences,
  dayjsFormat,
  daysSince,
  formatDollarAmount,
  formatPhoneNumber,
  getDigitsFromPhone,
  getEnumValue,
  getIdFromName,
  getKeysFromObjects,
  getNameFrom_Id,
  getSalesPersonIdFromName,
  getValueByKeyAndMatch,
  hasValues,
  isSuccess,
  notify,
  splitFullName,
} from '../../../../shared/helpers/util'
import { InputWithValidation } from '../../../../shared/inputWithValidation/InputWithValidation'
import { SharedPhone } from '../../../../shared/sharedPhone/SharedPhone'
import { Table } from '../../../../shared/table/Table'
import * as SharedStyled from '../../../../styles/styled'
import { colors } from '../../../../styles/theme'
import { AddressWrap, IntendWidth } from '../../style'
import { AddCityModal } from '../addCityModal/AddCityModal'
import * as Styled from './style'
import { getCampaigns, getLeadSources } from '../../../../logic/apis/leadSource'
import Toggle from '../../../../shared/toggle/Toggle'
import { getReferres, getSalesPersonAndPM } from '../../../../logic/apis/company'
import '../../../../shared/helpers/yupExtension'
import { AddNewContactModal, I_Contacts, mergeSourceAndCampaignNames } from '../addNewContactModal/AddNewContactModal'
import Contacts from '../addNewContactModal/components/ContactCard'
import UnitSvg from '../../../../assets/newIcons/unitModal.svg'
import {
  getFormattedLeadSrcData,
  getLeadSrcDropData,
  getLeadSrcDropdownId,
  getLeadSrcDropdownName,
} from '../../../leadSource/LeadSource'
import useFetch from '../../../../logic/apis/useFetch'
import AutoCompleteIndentation from '../../../../shared/autoCompleteIndentation/AutoCompleteIndentation'
import Tracking from './components/Tracking'
import Actions from './components/Actions'
import {
  addLinkedContact,
  deleteContact,
  getContactActivity,
  getContactById,
  getContactLeads,
  getContactOpportunities,
  getContactReferrals,
  getContacts,
  getLinkedContact,
  getSearchedContact,
  markLeadAsInvalid,
  migrateContact,
  permanentDeleteContact,
  removeLinkedContact,
  restoreContact,
  updateContact,
  updateLead,
} from '../../../../logic/apis/contact'
import { getProjectTypes } from '../../../../logic/apis/projects'
import { SharedDate } from '../../../../shared/date/SharedDate'
import SearchableDropdown from '../../../../shared/searchableDropdown/SearchableDropdown'
import ContactCard from '../addNewContactModal/components/ContactCard'
import { AddRelationshipContact } from '../addRelationshipContactModal/AddRelationshipContact'
import { SLoader } from '../../../../shared/components/loader/Loader'
import { LoadScript } from '@react-google-maps/api'
import { getConfig } from '../../../../config'
import { SharedDateAndTime } from '../../../../shared/date/SharedDateAndTime'
import { Types } from '../../constant'
import ContactCardToBeAdded from '../addNewContactModal/components/ContactCardToBeAdded'
import CommentsProfile from './components/CommentsProfile'
import ToDoNextProfile from './components/ToDoNextProfile'
import Activity from '../../../opportunity/components/activity/Activity'
import TeamMembers from './components/TeamMembers'
import AddOpportunityModal, { I_Position, I_SalesPerson } from '../../../sales/AddOpportunityModal'
import { getDepartments } from '../../../../logic/apis/department'
import ReferrerModal from '../../../Refferer/components/referrerModal/ReferrerModal'
import LostLeadModal from '../../../leads/LostLeadModal'
import LeadStage from './components/LeadStage'
import { getStages, updateActivity } from '../../../../logic/apis/sales'
import { Nue, StageGroupEnum } from '../../../../shared/helpers/constants'
import LeadInfo from './components/LeadInfo'
import InvalidLeadModal from '../../../leads/InvalidLeadModal'
import AddLeadWarrantyModal from '../../../operations/component/AddLeadWarrantyModal'
import InfoWarrantyModal from './components/InfoWarrantyModal'
import { CreateRuleDialog } from '../../../marketingChannel/components/createRuleDialog/CreateRuleDialog'
import ContactMergeModal from './components/ContactMergeModal'
import ErrorBoundary from '../../../app/errorBoundary/ErrorBoundary'
import { TextAreaWithValidation } from '../../../../shared/textAreaWithValidation'

export const fetchSearchReferer = async (query: string, isAllreferrer = true) => {
  try {
    const res = await getReferres(false, isAllreferrer, { search: query })
    if (isSuccess(res)) {
      return res
    } else {
      return null
    }
  } catch (error) {
    console.error({ error })
    return null // or [] or undefined
  }
}

export const getReferralNameFromID = async (referrerId: string, isAllreferrer = true) => {
  try {
    const res = await getReferres(false, isAllreferrer, { referredBy: referrerId })
    if (isSuccess(res)) {
      const result = res?.data?.data?.referrers?.filter((item: any) => item._id === referrerId)[0]?.name || 'Unknown'

      console.log('result===[log]===>', result)
      return result
    } else {
      return null
    }
  } catch (error) {
    console.error({ error })
    return null // or [] or undefined
  }
}

/**
 * InitialValues is an interface declared here so that it can be used as a type for the useState hook
 */
interface InitialValues {
  businessName: string
  firstName: string
  lastName: string
  city: string
  street: string
  state: string
  zip: string
  phone: string
  email: string
  tags: string[]
  contacts: I_Contacts[]
  tracking: any
  leadSourceName: string
  referredBy: string
  invalidLeadNote: string
  appointmentSetter: string
  salesRep: string
  lostDate: string
  projectManager: string
  lostNote: string
  notes: string
  type: string
  isBusiness: boolean
  // businessName?: string
  fullName?: string
  dateReceived: string
  workType: string
  firstPurchase: string
  invalidLeadReason: string
  lostReason: string
  dateOfBirth: string
}

interface I_Data {
  name: string
  project: string
  price: string
  workType: string
  property: string
  value: string
}

const ContactProfile = () => {
  /**
   * This initialValues is the state which is passed to the Formik prop: initialValues
   */
  const [initialValues, setInitialValues] = useState<InitialValues>({
    businessName: '',
    firstName: '',
    lastName: '',
    city: '',
    street: '',
    state: '',
    zip: '',
    phone: '',
    contacts: [],
    tags: [],
    dateReceived: '',
    lostDate: '',
    type: '',
    workType: '',
    firstPurchase: '',
    invalidLeadReason: '',
    invalidLeadNote: '',
    lostNote: '',
    lostReason: '',
    dateOfBirth: '',
    tracking: {},
    email: '',
    leadSourceName: '',
    referredBy: '',
    notes: '',
    isBusiness: false,
    appointmentSetter: '',
    salesRep: '',
    projectManager: '',
    // businessName: '',
  })

  /**
   * loading will be the loading state when performing the operations
   */

  const [deleteLoading, setDeleteLoading] = useState<boolean>(false)
  const [permanentdeleteLoading, setPermanentDeleteLoading] = useState<boolean>(false)
  const [unDeleteLoading, setUnDeleteLoading] = useState<boolean>(false)
  // const [lat, setLat] = useState('')
  // const [long, setLong] = useState('')
  const [shimmerLoading, setShimmerLoading] = useState<boolean>(true)
  const [loadingStates, setLoadingStates] = useState({
    oppLoading: true,
    contactLoading: true,
    leadLoading: true,
  })

  const [refererres, setRefererres] = useState<any>([])
  const [toggleHeading, setToggleHeading] = useState<{ [key: string]: boolean }>({ '0': true, LeadInfo: true })
  const [showAddCityModal, setShowAddCityModal] = useState<boolean>(false)
  const [dataUpdate, setDataUpdate] = useState<boolean>(false)

  const [showEditAddress, setShowEditAddress] = useState(false)
  const [leadsrcDrop, setLeadsrcDrop] = useState([])
  const [leadSrcData, setLeadSrcData] = useState([])
  const [showAddContact, setShowAddContact] = useState<boolean>(false)
  const [addContact, setAddContact] = useState<boolean>(false)
  const [contactIndex, setContactIndex] = useState(-1)
  const [salesPersonDrop, setSalesPersonDrop] = useState<I_SalesPerson[]>([])
  const [projectManagerDrop, setProjectManagerDrop] = useState<I_SalesPerson[]>([])
  const [opportunityData, setOpportunityData] = useState([])
  const [referralOpps, setReferralOpps] = useState([])
  const [selectedLeadId, setSelectedLeadId] = useState('')
  const [lostModal, setLostModal] = useState(false)
  const [invalidLeadModal, setInvalidLeadModal] = useState(false)
  const [warrentyModal, setWarrentyModal] = useState(false)
  const [createNew, setCreateNew] = useState(false)
  const [addNewClientModal, setShowAddNewClientModal] = useState(false)
  const [detailsUpdate, setDetailsUpdate] = useState(false)
  const [clientAutoFill, setClientAutoFill] = useState<any>({})
  const [createdClient, setCreatedClient] = useState({})

  const [infoWarrentyModal, setInfoWarrentyModal] = useState(false)
  const [stages, setStages] = useState<any[]>([])
  const [isBusiness, setIsBusiness] = useState()
  const [selectedLeadSourceObject, setSelectedLeadSourceObject] = useState<any>()
  const [relationshipContact, setAddRelationshipContact] = useState<any>({})
  const [getAtivity, setActivity] = useState<any>([])
  const [activityLoading, setActivityLoading] = useState(false)
  const [officeDrop, setOfficeDrop] = useState<any[]>([])
  const [showAddModal, setShowAddModal] = useState(false)
  const [referrerModal, setShowReferrerModal] = useState(false)
  const [referrerValue, setReferrerValue] = useState<any>([])
  const [contactDetails, setContactDetails] = useState<any>({})
  const [leads, setLeads] = useState<any>([])
  const [clientData, setClientData] = useState<any>({})
  const [clientName, setClientName] = useState<any>('')
  const [editClientModal, setShowEditClientModal] = useState(false)
  const [originalValues, setOriginalValues] = useState<any>(null)
  const [showCreateRuleDialog, setShowCreateRuleDialog] = useState(false)
  const [isLost, setIsLost] = useState(false)
  const [trackingDataTag, setTrackingDataTag] = useState<any>({})
  const [isInvalid, setIsInvalid] = useState(contactDetails?.isInvalid || false)
  const [trackingRuleId, setTrackingRuleId] = useState('')
  const [localReferrerName, setLocalReferrerName] = useState('')

  const { contactId, isDeleted } = useParams()
  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember, companySettingForAll } = globalSelector.company

  useEffect(() => {
    if (leadSrcData?.length) {
      const data = getLeadSrcDropData(leadSrcData)
      setLeadsrcDrop(data)
    }
  }, [leadSrcData?.length])

  /**
   * ContactProfileSchema is a ValidationSchema which is passed to the Formik prop: validationSchema, here we add validation to all the input fields using yup
   */
  const ContactProfileSchema = Yup.object().shape({
    // firstName: Yup.string().when('businessName', (value, schema) => {
    //   if (value && isBusiness) return schema.optional()
    //   return schema.min(1, 'Too Short!').max(50, 'Too Long!').required('Required').matches(onlyText, 'Enter Valid Name')
    // }),
    firstName: Yup.string().required('Required'),
    lastName: Yup.string().min(1, 'Too Short!').max(50, 'Too Long!'),
    leadSourceName: Yup.string(),
    notes: Yup.string(),
    street: Yup.string(),
    city: Yup.string(),
    state: Yup.string(),
    zip: Yup.string().matches(onlyNumber, 'Must be a number'),
    phone: Yup.string().test('phone-or-email', 'Either phone or email is required', function (value) {
      return value || this.parent.email
    }),
    email: Yup.string()
      .email('Invalid email')
      .test('phone-or-email', 'Either phone or email is required', function (value) {
        return value || this.parent.phone
      }),
  })

  const [tableLoading, setTableLoading] = useState(false)
  const navigate = useNavigate()

  const [projectTypesDrop, setProjectTypesDrop] = useState<any>([])
  const [linkedContactData, setLinkedContactData] = useState<any>([])
  const [linkedContactLoading, setLinkedContactLoading] = useState(true)

  useEffect(() => {
    if (contactId) {
      initFetch()
      isDeleted !== 'true' && fetchLinkedContact()
      fetchContactLeads()
    }
  }, [contactId])

  const fetchLinkedContact = async () => {
    setLinkedContactLoading(true)
    try {
      const res = await getLinkedContact(contactId!)
      if (isSuccess(res)) {
        setLinkedContactData(res?.data?.data?.linkedContacts)
      }
    } catch (error) {
      console.log({ error })
    } finally {
      setLinkedContactLoading(false)
    }
  }

  const initFetch = async () => {
    try {
      const res = await getProjectTypes({ deleted: false })
      if (isSuccess(res)) {
        const { projectType } = res.data.data
        const object = projectType.map(({ _id, name }: { _id: string; name: string }) => ({
          name: name,
          id: _id,
          value: _id,
          label: name,
        }))
        setProjectTypesDrop([
          ...object,
          {
            name: 'Unknown',
            id: 'unknown',
            value: 'unknown',
            label: 'unknown',
          },
        ])
      } else throw new Error(res?.data?.message)
    } catch (err) {
      console.log('Failed init fetch', err)
    }
  }

  const fetchSearchContact = async (query: string) => {
    try {
      const res = await getSearchedContact(query, {
        fields: { fullName: 1, firstName: 1, lastName: 1, email: 1, phone: 1, street: 1, city: 1, state: 1, zip: 1 },
      })
      if (isSuccess(res)) {
        return res // ✅ return the successful result
      } else {
        return null // or [] or undefined depending on expected return shape
      }
    } catch (error) {
      console.error({ error })
      return null // or [] or undefined
    }
  }

  const getOpportunities = async () => {
    setTableLoading(true)
    try {
      const res = await getContactOpportunities(contactId!)

      if (isSuccess(res)) {
        setOpportunityData(
          res?.data?.data?.clientOpps?.sort((a: any, b: any) => {
            const dateA = new Date(a?.oppDate)?.getTime()
            const dateB = new Date(b?.oppDate)?.getTime()
            return dateB - dateA
          })
        )
        setLoadingStates((prev) => ({ ...prev, oppLoading: false }))
      }
    } catch (error) {
      console.error('Error=====>', error)
    } finally {
      setTableLoading(false)
    }
  }

  const fetchContactLeads = async () => {
    try {
      const response = await getContactLeads(contactId!)
      if (isSuccess(response)) {
        const { leads } = response?.data?.data

        setLeads(leads)
        const tracking = leads?.[0]?.tracking || {}
        const selectedTracking = {
          adName: tracking.adName || '',
          sessionSource: tracking.sessionSource || '',
          utmContent: tracking.utmContent || '',
          utmCampaign: tracking.utmCampaign || '',
          utmMedium: tracking.utmMedium || '',
          utmSource: tracking.utmSource || '',
        }
        setTrackingDataTag(selectedTracking)
        setIsLost(leads?.[0]?.status === 'lost')
        setIsInvalid(leads?.[0]?.status === 'invalid')
        setLoadingStates((prev) => ({ ...prev, leadLoading: false }))
      }
    } catch (error) {
      console.log(error)
    }
  }

  useEffect(() => {
    initFetchReferrers()
  }, [])
  // useEffect(() => {
  //   if (referrerValue === '--Add New--') {
  //     setShowReferrerModal(true)
  //   }
  // }, [referrerValue])
  const initFetchReferrers = async () => {
    try {
      const res = await getReferres(false, true)
      if (isSuccess(res)) {
        const { referrers } = res?.data?.data
        setRefererres(referrers)
      }
    } catch (error) {
      console.log(error)
    }
  }

  const columns: any = useMemo(
    () => [
      {
        Header: 'Name',
        accessor: (row: any) => `${row?.fullName}`,
      },
      {
        Header: 'PO#',
        accessor: (row: any) => `${row?.PO}-${row?.num}`,
      },
      {
        Header: 'Date',
        accessor: (row: any) => `${dayjsFormat(row?.oppDate, 'M/D/YY')}`,
      },
      {
        Header: 'Price',
        accessor: (row: any) => `${row?.soldValue ? formatDollarAmount(row?.soldValue) : '----'}`,
      },
      {
        Header: 'Work Type',
        accessor: (row: any) => row?.oppType?.name,
      },
      {
        Header: 'Property',
        accessor: 'street',
        Cell: (props: any) => {
          return (
            <AddressWrap>
              {props?.row?.original?.street ? <p>{props?.row?.original?.street},</p> : null}
              {props?.row?.original?.city ? (
                <p>
                  {props?.row?.original?.city}, {props?.row?.original?.state} {props?.row?.original?.zip}
                </p>
              ) : null}
            </AddressWrap>
          )
        },
      },
    ],
    []
  )

  useEffect(() => {
    window?.scrollTo(0, 0)
    isDeleted !== 'true' && getOpportunities()
  }, [])

  const deleteContactFunc = async () => {
    try {
      setDeleteLoading(true)

      let response = await deleteContact(contactId!)
      if (isSuccess(response)) {
        notify('Contact Profile Deleted Successfully', 'success')
        setDeleteLoading(false)
        navigate(`/contact`)
      } else {
        setDeleteLoading(false)
        notify(response?.data?.message, 'error')
      }
    } catch (error) {
      console.error('deleteContactFunc error', error)
    }
  }

  const permanentDeleteContactFunc = async () => {
    try {
      setPermanentDeleteLoading(true)

      let response = await permanentDeleteContact(contactId!)
      if (isSuccess(response)) {
        notify('Contact Profile Deleted Permanently', 'success')
        setPermanentDeleteLoading(false)
        navigate(`/contact`)
      } else {
        setPermanentDeleteLoading(false)
        notify(response?.data?.message, 'error')
      }
    } catch (error) {
      console.error('deleteContactFunc error', error)
    }
  }

  const restoreContactFunc = async () => {
    try {
      setUnDeleteLoading(true)

      let response = await restoreContact(contactId!)
      if (isSuccess(response)) {
        notify('Contact Profile Restored Successfully', 'success')
        setUnDeleteLoading(false)
        navigate(`/contact`)
      } else {
        setUnDeleteLoading(false)
        notify(response?.data?.message, 'error')
      }
    } catch (error) {
      console.error('deleteContactFunc error', error)
    }
  }

  const getContactDetails = async () => {
    try {
      // if (Object.keys(currentCompany).length > 0) {
      let clId: any = contactId
      const clientResponse = await getContactById(clId)

      const referralOpps = await getContactReferrals(clId!)

      setReferralOpps(referralOpps?.data?.data?.opps)

      if (clientResponse?.data?.statusCode === 200) {
        let statusRes = clientResponse?.data?.data?.contact || {}
        let dataObj = {
          ...statusRes,
          // firstName: statusRes?.isBusiness ? statusRes?.fullName : statusRes?.firstName,
        }
        delete dataObj.updatedAt
        delete dataObj.createdBy
        delete dataObj.createdAt
        delete dataObj.__v
        delete dataObj.companyId
        delete dataObj.deleted
        setContactDetails(dataObj)
        const localName = await getReferralNameFromID(dataObj?.referredBy)
        setLocalReferrerName(localName)
        setIsBusiness(dataObj?.isBusiness || false)
        setInitialValues({
          ...dataObj,
          referredBy: localName,
          tracking: dataObj?.tracking || {},
          dateReceived: dayjsFormat(dataObj?.dateReceived, 'YYYY-MM-DDTHH:mm') || '',
          dateOfBirth: dayjsFormat(dataObj?.dateOfBirth, 'YYYY-MM-DD') || '',
          firstPurchase: dayjsFormat(dataObj?.firstPurchase, 'YYYY-MM-DD') || '',
          salesRep: getValueByKeyAndMatch('name', dataObj?.salesPersonId, '_id', salesPersonDrop) || '',
          appointmentSetter: getValueByKeyAndMatch('name', dataObj?.csrId, '_id', officeDrop) || '',
          projectManager: getValueByKeyAndMatch('name', dataObj?.projectManagerId, '_id', projectManagerDrop) || '',
          // contacts: linkedContactData?.linkedContacts || [],
          type: Object.entries(Types).find(([_, v]) => v === dataObj?.type)?.[0] || '',
          workType: getValueByKeyAndMatch('name', dataObj?.workType, 'id', projectTypesDrop),
          // leadSourceName: getNameFrom_Id(dataObj?.leadSource, leadsrcDrop),
          leadSourceName: getLeadSrcDropdownName(dataObj?.campaignId || dataObj?.leadSourceId, leadSrcData)?.sourceName,
        })
        setOriginalValues({
          businessName: dataObj?.businessName || '',
          firstName: dataObj?.firstName,
          lastName: dataObj?.lastName,
          city: dataObj?.city,
          street: dataObj?.street,
          state: dataObj?.state,
          zip: dataObj?.zip,
          phone: dataObj?.phone,
          email: dataObj?.email,
          notes: dataObj?.notes || '',
          type: Object.entries(Types).find(([_, v]) => v === dataObj?.type)?.[0] || '',
          dateReceived: dataObj?.newLeadDate || '',
          leadSourceName: getLeadSrcDropdownName(dataObj?.campaignId || dataObj?.leadSourceId, leadSrcData)?.sourceName,
          referredBy: await getReferralNameFromID(dataObj?.referredBy),
          workType: getValueByKeyAndMatch('name', dataObj?.workType, 'id', projectTypesDrop),
          dateOfBirth: dataObj?.dateOfBirth || '',
          firstPurchase: dataObj?.firstPurchase || '',
          salesRep: getValueByKeyAndMatch('name', dataObj?.salesPersonId, '_id', salesPersonDrop) || '',
          appointmentSetter: getValueByKeyAndMatch('name', dataObj?.csrId, '_id', officeDrop) || '',
          projectManager: getValueByKeyAndMatch('name', dataObj?.projectManagerId, '_id', projectManagerDrop) || '',
          // contacts: linkedContactData?.linkedContacts || [],
        })
        setLoadingStates((prev) => ({ ...prev, contactLoading: false }))
      } else {
        notify(clientResponse?.data?.message, 'error')
      }
      // }
    } catch (error) {
      console.error('getDetails error', error)
    } finally {
      setShimmerLoading(false)
    }
  }

  const getPositionMembers = async () => {
    try {
      const response = await getSalesPersonAndPM() // NHR-1570
      if (isSuccess(response)) {
        setSalesPersonDrop(response?.data?.data?.members)
      } else notify(response?.data?.message, 'error')
    } catch (err) {
      console.log('GET POSITION MEMBERS FAILED', err)
    }
  }

  const getPositionMembersForSalesManager = async () => {
    try {
      const response = await getSalesPersonAndPM(undefined, true) // NHR-1570
      if (isSuccess(response)) {
        setProjectManagerDrop(response?.data?.data?.members)
      } else notify(response?.data?.message, 'error')
    } catch (err) {
      console.log('GET POSITION MEMBERS FAILED', err)
    }
  }

  useEffect(() => {
    if (
      refererres.length &&
      salesPersonDrop.length &&
      projectManagerDrop.length &&
      officeDrop.length &&
      leadsrcDrop.length &&
      leadSrcData.length &&
      projectTypesDrop?.length
    ) {
      getContactDetails()
    }
  }, [
    dataUpdate,
    refererres,
    leadsrcDrop,
    salesPersonDrop,
    projectManagerDrop,
    officeDrop,
    leadSrcData,
    projectTypesDrop,
  ])

  const getLeadSrcData = async () => {
    try {
      const leadSourceResponse = await getLeadSources({ limit: '100', active: true }, false)
      if (leadSourceResponse?.data?.statusCode === 200) {
        let statusRes = leadSourceResponse?.data?.data?.leadSource
        setLeadSrcData(statusRes)
      } else notify(leadSourceResponse?.data?.message, 'error')
    } catch (err) {
      // notify('Failed to fetch lead sources!', 'error')
      console.log('Lead source fetch error', err)
    }
  }

  const fetchActivity = async () => {
    try {
      setActivityLoading(true)
      const res = await getContactActivity(contactId!)
      if (isSuccess(res)) {
        const { activities } = res?.data?.data
        setActivity(activities)
      }
    } catch (error) {
      console.log(error)
    } finally {
      setActivityLoading(false)
    }
  }

  const getPositionsOffice = async () => {
    try {
      const response = await getDepartments({}, false)
      if (isSuccess(response)) {
        console.log({ response })
        const departments: I_Position[] = response?.data?.data?.department

        let officePersonIdx: string[] = []
        departments.forEach((department: any, idx) => {
          if (department.name === 'Office') {
            officePersonIdx.push(department?._id)
            return
          }
        })
        getPositionMembersForOffice(officePersonIdx?.join())
      } else {
        notify(response?.data?.message, 'error')
      }
    } catch (err) {
      console.log('GET POSITION FAILED', err)
    }
  }

  const getPositionMembersForOffice = async (departmentId: string) => {
    try {
      const response = await getSalesPersonAndPM(departmentId) // NHR-1567
      if (isSuccess(response)) {
        setOfficeDrop(response?.data?.data?.members)
      } else notify(response?.data?.message, 'error')
    } catch (err) {
      console.log('GET POSITION MEMBERS FAILED', err)
    }
  }

  useEffect(() => {
    getLeadSrcData()
    fetchActivity()
    getPositionMembersForSalesManager()
    getPositionMembers()
    getPositionsOffice()
  }, [])

  useEffect(() => {
    const fetchStages = async () => {
      try {
        const response = await getStages({}, false, StageGroupEnum.Leads)
        if (isSuccess(response)) {
          setStages(response.data.data.stage || [])
        }
      } catch (err) {
        console.error('Error fetching stages:', err)
      } finally {
      }
    }

    fetchStages()
  }, [])

  const toggleCount = (type: string) => {
    setToggleHeading((prevState) => ({
      ...prevState,
      [type]: !prevState[type],
    }))
  }

  const toggleAllIndexes = () => {
    const allExpanded = Object.values(toggleHeading).every((val) => val === true)

    setToggleHeading(() => {
      const updated: Record<string, boolean> = {}
      leads.forEach((_: any, idx: number) => {
        updated[idx] = !allExpanded // Toggle based on current state
      })
      return updated
    })
  }

  const handleRemove = async (id: string) => {
    try {
      const res = await removeLinkedContact(contactId!, id)
      if (isSuccess(res)) {
        notify('Linked Contact Removed Successfully', 'success')
        fetchLinkedContact()
      }
    } catch (error) {
      console.log({ error })
    }
  }

  const makeLost = async () => {
    setLostModal(true)
  }
  const handleInvalidLeadClick = () => {
    setInvalidLeadModal(true)
  }

  function isLeadOverTwoWeeksOld(dateString: string): boolean {
    const leadTime = new Date(dateString).getTime()
    return daysSince(leadTime) > 14
  }

  const handleInputBlurValue = async (data: any, activityString: string, resetCallback?: () => void) => {
    const hasValues = Object.entries(data).filter(([_, value]) => {
      // Keep boolean values (both true and false) as valid values
      if (typeof value === 'boolean') return true
      // For other types, filter out falsy values
      return Boolean(value)
    })

    if (!hasValues?.length) {
      const keys = Object.keys(data)
      notify(`Please enter ${keys[0]}`, 'error')
      return
    }

    // Validate first name must exist
    const finalFirstName = data.firstName?.trim() || contactDetails?.firstName?.trim()
    if (!finalFirstName) {
      notify('First name is required', 'error')
      return
    }
    const finalLastName = data.hasOwnProperty('lastName')
      ? data.lastName?.trim()
      : contactDetails?.lastName?.trim() || ''

    const finalFullName = `${finalFirstName} ${finalLastName}`.trim()

    try {
      // Always include these required fields
      const requiredData = {
        ...data,
        firstName: finalFirstName,
        lastName: finalLastName,
        fullName: finalFullName,
        type: Types[data.type as keyof typeof Types] || contactDetails?.type,
      }

      const res = await updateContact(requiredData, contactId!)

      if (isSuccess(res)) {
        notify('Contact updated successfully', 'success')

        // Update activity log if provided
        if (activityString) {
          const resActivity = await updateActivity({
            id: contactId!,
            memberId: currentMember._id!,
            body: activityString,
            currDate: new Date().toISOString(),
          })
          if (isSuccess(resActivity)) {
            fetchActivity()
          }
        }

        // Refresh contact details
        getContactDetails()
      } else {
        notify(res?.data?.message, 'error')
      }
    } catch (error) {
      console.error('Update contact error:', error)
    } finally {
      resetCallback?.()
    }
  }

  const hasValueChanged = (initialValue: string | number | boolean, finalValue: string | number | boolean) => {
    return initialValue !== finalValue
  }

  return (
    <>
      {shimmerLoading ? (
        <>
          <SharedStyled.Skeleton custWidth="100%" custHeight={'51px'} />
          <SharedStyled.Skeleton custWidth="100%" custHeight="50%" custMarginTop="10px" />
          <SharedStyled.Skeleton custWidth="100%" custHeight="50%" custMarginTop="10px" />
        </>
      ) : (
        <Formik
          initialValues={initialValues}
          onSubmit={() => {}}
          enableReinitialize={true}
          validateOnChange={true}
          validateOnBlur={false}
        >
          {({ errors, touched, values, setFieldValue, handleChange }) => {
            useEffect(() => {
              if (values.leadSourceName !== '') {
                const result = getLeadSrcDropdownId(values.leadSourceName || '', leadSrcData)
                setSelectedLeadSourceObject(result.leadSourceObject)
              }
            }, [values.leadSourceName])

            const handleRemoveToBeAdded = (index: number, contacts: any[]) => {
              const updated = [...contacts]
              updated.splice(index, 1)
              setFieldValue('contacts', updated || [])
            }
            const leadDate = leads?.[0]?.newLeadDate
            const leadStatus = leads?.find((v) => v.status === 'active') ? true : false

            return (
              <>
                <SharedStyled.FlexRow margin="0 0 20px 0">
                  <SharedStyled.SectionTitle>Contact Profile</SharedStyled.SectionTitle>

                  <Button
                    type="button"
                    disabled={Object.values(loadingStates).some((val) => val === true)}
                    width="max-content"
                    onClick={() => {
                      setShowAddModal(true)
                      // if (opportunityData?.length) {
                      //   setFoundOppModal(true)
                      // } else {
                      //   if (!leadStatus) {
                      //     setShowAddModal(true)
                      //   } else if (isLeadOverTwoWeeksOld(leadDate)) {
                      //     setInfoOpportunityModal(true)
                      //   } else {
                      //     setShowAddModal(true)
                      //   }
                      // }
                    }}
                  >
                    Add Opportunity
                  </Button>

                  {leadStatus ? (
                    <>
                      {!isLost && !isInvalid && (
                        <Button
                          disabled={!leads[0]?._id}
                          type="button"
                          width="max-content"
                          className={'gray'}
                          onClick={handleInvalidLeadClick}
                        >
                          {'Invalid Lead'}
                        </Button>
                      )}
                      {!isInvalid && !isLost && (
                        <Button
                          type="button"
                          disabled={!leads[0]?._id}
                          width="max-content"
                          className={'delete'}
                          onClick={makeLost}
                        >
                          {'Lose'} Lead
                        </Button>
                      )}
                    </>
                  ) : null}

                  {contactDetails?.type === Types.Client ? (
                    <>
                      <Button
                        type="button"
                        width="max-content"
                        className="yellow-white-text"
                        onClick={() => {
                          setWarrentyModal(true)
                          // const leadDate = leads?.[0]?.newLeadDate
                          // const leadStatus = leads?.[0]?.status === 'active' ? true : false
                          // if (!leadStatus) {
                          //   setWarrentyModal(true)
                          // } else if (isLeadOverTwoWeeksOld(leadDate)) {
                          //   alert('Lead is over 2 weeks old')
                          //   setWarrentyModal(true)
                          // } else {
                          //   setInfoWarrentyModal(true)
                          // }
                        }}
                      >
                        Create Warranty
                      </Button>
                    </>
                  ) : null}
                </SharedStyled.FlexRow>
                <Styled.ContactContainer>
                  <Styled.ClientProfileContainer>
                    <LoadScript
                      googleMapsApiKey={getConfig().googleAddressApiKey}
                      //  @ts-ignore
                      libraries={['places']}
                      loadingElement={<SLoader height={35} width={100} isPercent />}
                    >
                      <Form className="form">
                        <SharedStyled.Content
                          className="hide-scroll"
                          width="100%"
                          disableBoxShadow={true}
                          noPadding={true}
                        >
                          <Toggle
                            title="Business"
                            isToggled={isBusiness}
                            onToggle={() => {
                              setIsBusiness((prev) => !prev)
                              handleInputBlurValue(
                                {
                                  isBusiness: !isBusiness,
                                },
                                `changed Business from ${contactDetails?.isBusiness || false} to ${!isBusiness}`,
                                () => {}
                              )
                            }}
                          />
                          {isBusiness ? (
                            <InputWithValidation
                              labelName="Business Name*"
                              stateName="businessName"
                              error={touched.businessName && errors.businessName ? true : false}
                              twoInput={true}
                              onBlur={() => {
                                if (hasValueChanged(contactDetails?.businessName, values.businessName)) {
                                  handleInputBlurValue(
                                    {
                                      businessName: values.businessName?.trim(),
                                    },
                                    `changed Business Name from ${contactDetails?.businessName || 'None'} to ${
                                      values.businessName
                                    }`,
                                    () => {}
                                  )
                                }
                              }}
                            />
                          ) : null}
                          <SharedStyled.TwoInputDiv>
                            <InputWithValidation
                              labelName="Primary First Name*"
                              stateName="firstName"
                              error={touched.firstName && errors.firstName ? true : false}
                              twoInput={true}
                              onBlur={async () => {
                                if (hasValueChanged(contactDetails?.firstName, values.firstName)) {
                                  handleInputBlurValue(
                                    {
                                      firstName: values.firstName?.trim(),
                                      lastName: values.lastName?.trim(),
                                    },
                                    `changed First Name from ${contactDetails?.firstName || 'None'} to ${
                                      values.firstName
                                    }`,
                                    () => {}
                                  )
                                }
                              }}
                            />

                            <InputWithValidation
                              labelName="Primary Last Name"
                              stateName="lastName"
                              error={touched.lastName && errors.lastName ? true : false}
                              twoInput={true}
                              onBlur={async () => {
                                if (hasValueChanged(contactDetails?.lastName, values.lastName)) {
                                  handleInputBlurValue(
                                    {
                                      firstName: values.firstName?.trim(),
                                      lastName: values.lastName?.trim(),
                                    },
                                    `changed Last Name from ${contactDetails?.lastName || 'None'} to ${
                                      values.lastName
                                    }`,
                                    () => {}
                                  )
                                }
                              }}
                            />
                          </SharedStyled.TwoInputDiv>

                          <SharedStyled.TwoInputDiv>
                            <SharedPhone
                              labelName="Primary Phone"
                              stateName="phone"
                              onChange={handleChange('phone')}
                              value={values.phone}
                              error={touched.phone && errors.phone ? true : false}
                              onBlur={() => {
                                if (hasValueChanged(contactDetails?.phone, values.phone)) {
                                  handleInputBlurValue(
                                    {
                                      phone: values.phone,
                                    },
                                    `changed Phone from ${formatPhoneNumber(
                                      contactDetails?.phone,
                                      ''
                                    )} to ${formatPhoneNumber(values.phone, '')}`,
                                    () => {}
                                  )
                                }
                              }}
                            />

                            <InputWithValidation
                              labelName="Primary Email"
                              stateName="email"
                              error={touched.email && errors.email ? true : false}
                              onBlur={() => {
                                if (hasValueChanged(contactDetails?.email, values.email)) {
                                  handleInputBlurValue(
                                    {
                                      email: values.email,
                                    },
                                    `changed Email from ${contactDetails?.email || 'None'} to ${values.email}`,
                                    () => {}
                                  )
                                }
                              }}
                            />
                          </SharedStyled.TwoInputDiv>

                          <SharedStyled.FlexCol gap="6px">
                            <br />
                            <div>
                              {' '}
                              <SharedStyled.Text
                                color={`${colors.darkGrey}`}
                                textAlign="left"
                                fontWeight="bold"
                                fontSize="16px"
                              >
                                Linked Contacts{' '}
                              </SharedStyled.Text>
                              <Button
                                padding="4px 10px"
                                width="max-content"
                                type="button"
                                onClick={() => {
                                  setShowAddContact((pre) => !pre)
                                }}
                              >
                                +
                              </Button>
                            </div>
                            {showAddContact ? (
                              <IntendWidth>
                                <SharedStyled.FlexCol width="100%" gap="10px" alignItems="center">
                                  <SearchableDropdown
                                    height="280px"
                                    label="Search Contacts"
                                    placeholder="Type to search"
                                    searchFunction={fetchSearchContact}
                                    displayKey={'fullName'}
                                    onSelect={(item: any) => {
                                      setAddContact(true)
                                      setAddRelationshipContact(item)
                                      setContactIndex(values?.contacts?.length || 0)
                                    }}
                                    resultExtractor={(res) => res?.data?.data?.contacts || []}
                                    onAddClick={() => {
                                      setAddContact(true)
                                      setContactIndex(-1)
                                    }}
                                    addNewText="Add New"
                                    showAddOption
                                  />

                                  {addContact && (
                                    <AddRelationshipContact
                                      onClose={() => {
                                        setAddContact(false)
                                        setShowAddContact(false)

                                        setAddRelationshipContact({})
                                      }}
                                      companySettingForAll={companySettingForAll}
                                      contacts={values.contacts || []}
                                      setFieldValue={setFieldValue}
                                      index={contactIndex}
                                      relationshipContact={relationshipContact}
                                      contactId={contactId}
                                      fetchLinkedContact={fetchLinkedContact}
                                    />
                                  )}
                                </SharedStyled.FlexCol>
                              </IntendWidth>
                            ) : null}
                            <IntendWidth>
                              {linkedContactData?.map((v: any, index: number) => (
                                <ContactCard
                                  contact={v || {}}
                                  isLoading={linkedContactLoading}
                                  key={index}
                                  onRemove={() => handleRemove(v?.id?._id ?? '')}
                                  onClick={() => {
                                    window.location.href = `/contact/profile/${v?.id?._id}/${isDeleted}`
                                    // navigate(`/contact/profile/${v?.id?._id}/${isDeleted}`)
                                  }}
                                />
                              ))}
                            </IntendWidth>
                          </SharedStyled.FlexCol>

                          <SharedStyled.FlexRow margin="10px 0 0 0" justifyContent="flex-start" alignItems="flex-start">
                            <div style={{ width: '100%' }}>
                              <SharedStyled.FlexRow>
                                <b>Address - </b>
                                <p
                                  className="link"
                                  onClick={() => {
                                    if (!showEditAddress) {
                                      setShowEditAddress(true)
                                    } else {
                                      setShowEditAddress(false)

                                      handleInputBlurValue(
                                        {
                                          street: values.street,
                                          city: values.city,
                                          state: values.state,
                                          zip: values.zip,
                                          fullAddress:
                                            `${values.street}, ${values.city}, ${values.state} ${values.zip}`.trim(),
                                        },
                                        `changed Address from ${contactDetails?.street}, ${contactDetails?.city}, ${contactDetails?.state}, ${contactDetails?.zip} to ${values.street}, ${values.city}, ${values.state}, ${values.zip}`,
                                        () => {}
                                      )
                                    }
                                  }}
                                >
                                  {showEditAddress ? 'Confirm' : 'Edit'}
                                </p>
                              </SharedStyled.FlexRow>

                              {showEditAddress ? (
                                <>
                                  <AutoCompleteAddress
                                    setFieldValue={setFieldValue}
                                    street={'street'}
                                    city={'city'}
                                    state={'state'}
                                    zip={'zip'}
                                    sourceAddress={companySettingForAll?.address}
                                    companyLatLong={companySettingForAll}
                                    noLoadScript={true}
                                  />
                                </>
                              ) : (
                                <AddressWrap className="font">
                                  <p>{values?.street}</p>
                                  <p>
                                    {values?.city}, {values.state} {values.zip}
                                  </p>
                                </AddressWrap>
                              )}
                            </div>
                          </SharedStyled.FlexRow>

                          <TextAreaWithValidation
                            labelName="Notes"
                            stateName="notes"
                            height="52px"
                            marginTop="8px"
                            error={touched.notes && errors.notes ? true : false}
                            onBlur={() => {
                              if (hasValueChanged(contactDetails?.notes, values.notes)) {
                                handleInputBlurValue(
                                  {
                                    notes: values.notes,
                                  },
                                  `changed Notes from ${contactDetails?.notes || 'None'} to ${values.notes}`,
                                  () => {}
                                )
                              }
                            }}
                          />

                          {referralOpps?.length ? (
                            <SharedStyled.FlexRow flexDirection="column" alignItems="flex-start" margin="10px 0">
                              <b>Referrals</b>

                              <ul
                                style={{
                                  paddingLeft: '10px',
                                  margin: '0px',
                                }}
                              >
                                {referralOpps?.map((opp: any, index: number) => (
                                  <li key={index}>
                                    <Link
                                      target="_blank"
                                      to={`/${getEnumValue(opp?.stageGroup)}/opportunity/${opp?._id}`}
                                      style={{
                                        fontFamily: Nue.regular,
                                      }}
                                    >
                                      {opp?.PO}-{opp?.num} : {opp?.fullName} - {opp?.stageName}{' '}
                                      <span
                                        style={{
                                          textTransform: 'capitalize',
                                        }}
                                      >
                                        ({opp?.status})
                                      </span>{' '}
                                      - {dayjsFormat(opp?.newLeadDate, 'M/D/YY')}
                                    </Link>
                                  </li>
                                ))}
                              </ul>
                            </SharedStyled.FlexRow>
                          ) : null}

                          <CustomSelect
                            value={values.type}
                            labelName="Contact Type*"
                            stateName="type"
                            dropDownData={Object.keys(Types)}
                            setFieldValue={setFieldValue}
                            setValue={() => {}}
                            margin="10px 0 0 0"
                            error={touched.type && errors.type ? true : false}
                            onBlur={() => {
                              const oldType =
                                Object.entries(Types).find(([_, v]) => v === contactDetails?.type)?.[0] || ''
                              if (hasValueChanged(oldType, values.type)) {
                                handleInputBlurValue(
                                  {
                                    type: values.type,
                                  },
                                  `changed Contact Type from ${oldType || 'None'} to ${values.type}`,
                                  () => {}
                                )
                              }
                            }}
                          />

                          <SharedDateAndTime
                            value={values?.dateReceived || ''}
                            labelName="Date Received"
                            stateName="dateReceived"
                            error={!!(touched?.dateReceived && errors?.dateReceived)}
                            setFieldValue={setFieldValue}
                            onBlur={() => {
                              if (
                                hasValueChanged(
                                  dayjsFormat(contactDetails?.dateReceived || '', 'YYYY-MM-DDTHH:mm'),
                                  values?.dateReceived
                                )
                              )
                                handleInputBlurValue(
                                  {
                                    dateReceived: new Date(values?.dateReceived),
                                  },
                                  `changed Date Received from ${
                                    dayjsFormat(contactDetails?.dateReceived, 'MM/DD/YYYY HH:mm') || 'None'
                                  } to ${dayjsFormat(values?.dateReceived, 'MM/DD/YYYY HH:mm')}`
                                )
                            }}
                          />

                          {
                            <>
                              {leadSrcData?.length ? (
                                <AutoCompleteIndentation
                                  labelName="Lead Source"
                                  stateName={`leadSourceName`}
                                  isLeadSource
                                  dropdownHeight="180px"
                                  error={touched.leadSourceName && errors.leadSourceName ? true : false}
                                  borderRadius="0px"
                                  setFieldValue={setFieldValue}
                                  options={mergeSourceAndCampaignNames(leadSrcData)}
                                  formatedOptions={getFormattedLeadSrcData(leadSrcData)}
                                  value={values.leadSourceName!}
                                  setValueOnClick={(val: string) => {
                                    setFieldValue('leadSourceName', val)
                                  }}
                                  className="material-autocomplete"
                                  isIndentation={true}
                                  onBlur={(name: string) => {
                                    if (
                                      hasValueChanged(
                                        getLeadSrcDropdownName(
                                          contactDetails?.campaignId || contactDetails?.leadSourceId,
                                          leadSrcData
                                        )?.sourceName,
                                        name
                                      )
                                    ) {
                                      const result = getLeadSrcDropdownId(name, leadSrcData)
                                      const leadSourceId = result?.leadSourceId
                                      const campaignId = result?.campaignId || null
                                      handleInputBlurValue(
                                        {
                                          leadSourceId,
                                          campaignId,
                                        },
                                        `changed Lead Source from ${
                                          getLeadSrcDropdownName(
                                            contactDetails?.campaignId || contactDetails?.leadSourceId,
                                            leadSrcData
                                          )?.sourceName || 'None'
                                        } to ${name}`,
                                        () => {}
                                      )
                                    }
                                  }}
                                />
                              ) : null}

                              {selectedLeadSourceObject?.code === 'referral' && (
                                <SharedStyled.FlexBox width="100%" justifyContent="end">
                                  {/* <CustomSelect
                                    labelName="Referrer"
                                    stateName="referredBy"
                                    error={touched.referredBy && errors.referredBy ? true : false}
                                    setFieldValue={setFieldValue}
                                    setValue={() => {}}
                                    value={values.referredBy}
                                    dropDownData={getKeysFromObjects(refererres, 'name')}
                                    innerHeight="52px"
                                    margin="10px 0 0 0"
                                    // className="top"
                                    maxWidth="95%"
                                    onBlur={() => {
                                      if (
                                        hasValueChanged(
                                          getNameFrom_Id(contactDetails?.referredBy, refererres),
                                          values.referredBy
                                        )
                                      ) {
                                        handleInputBlurValue(
                                          {
                                            referredBy: getIdFromName(values.referredBy, refererres),
                                          },
                                          `changed Referrer from ${
                                            getNameFrom_Id(contactDetails?.referredBy, refererres) || 'None'
                                          } to ${values.referredBy}`,
                                          () => {}
                                        )
                                      }
                                    }}
                                  /> */}

                                  <SharedStyled.FlexRow width="95%">
                                    <SearchableDropdown
                                      label="Referrer"
                                      placeholder="Type to search"
                                      searchFunction={fetchSearchReferer}
                                      displayKey={'name'}
                                      refererOptions={refererres?.slice(0, 20)}
                                      onSelect={async (item: any) => {
                                        setFieldValue('referredBy', item?.name)

                                        const name = await getReferralNameFromID(contactDetails?.referredBy)

                                        if (hasValueChanged(name, item?.name)) {
                                          handleInputBlurValue(
                                            {
                                              referredBy: item?._id,
                                            },
                                            `changed Referrer from ${name || 'None'} to ${item?.name}`,
                                            () => {}
                                          )
                                        }
                                      }}
                                      selectedValue={values.referredBy}
                                      handleBlur={() => {}}
                                      resultExtractor={(res) => res?.data?.data?.referrers || []}
                                      showAddOption
                                      onAddClick={() => {
                                        setShowReferrerModal?.(true)
                                      }}
                                      showUnKnownOption
                                      onUnKnownClick={async () => {
                                        const name = await getReferralNameFromID(contactDetails?.referredBy)
                                        setFieldValue('referredBy', 'unknown')
                                        if (hasValueChanged(name, 'Unknown')) {
                                          handleInputBlurValue(
                                            {
                                              referredBy: 'unknown',
                                            },
                                            `changed Referrer from ${name || 'None'} to ${'Unknown'}`,
                                            () => {}
                                          )
                                        }
                                        setFieldValue('referredBy', 'Unknown')
                                      }}
                                    />
                                  </SharedStyled.FlexRow>
                                </SharedStyled.FlexBox>
                              )}
                            </>
                          }

                          <CustomSelect
                            labelName="Work Type"
                            stateName="workType"
                            value={values?.workType || ''}
                            error={!!(touched?.workType && errors?.workType)}
                            setFieldValue={setFieldValue}
                            setValue={() => {}}
                            dropDownData={[...projectTypesDrop.map(({ name }: { name: string }) => name)]}
                            innerHeight="52px"
                            margin="10px 0 0 0"
                            onBlur={() => {
                              const initialValue = getValueByKeyAndMatch(
                                'name',
                                contactDetails?.workType,
                                'id',
                                projectTypesDrop
                              )
                              if (hasValueChanged(initialValue, values.workType)) {
                                handleInputBlurValue(
                                  {
                                    workType: getValueByKeyAndMatch('id', values.workType, 'name', projectTypesDrop),
                                  },
                                  `changed Work Type from ${initialValue || 'None'} to ${values.workType}`,
                                  () => {}
                                )
                              }
                            }}
                          />

                          <SharedDate
                            value={values?.dateOfBirth || ''}
                            labelName="Date of Birth"
                            stateName="dateOfBirth"
                            error={!!(touched?.dateOfBirth && errors?.dateOfBirth)}
                            setFieldValue={setFieldValue}
                            onBlur={() => {
                              if (
                                hasValueChanged(
                                  dayjsFormat(contactDetails?.dateOfBirth, 'YYYY-MM-DD'),
                                  values?.dateOfBirth
                                )
                              )
                                handleInputBlurValue(
                                  {
                                    dateOfBirth: new Date(values?.dateOfBirth)?.toISOString(),
                                  },
                                  `changed Date of Birth from ${
                                    dayjsFormat(contactDetails?.dateOfBirth, 'MM/DD/YYYY') || 'None'
                                  } to ${dayjsFormat(values?.dateOfBirth, 'MM/DD/YYYY')}`
                                )
                            }}
                          />

                          <SharedDate
                            value={values?.firstPurchase || ''}
                            labelName="First Purchase"
                            stateName="firstPurchase"
                            error={!!(touched?.firstPurchase && errors?.firstPurchase)}
                            setFieldValue={setFieldValue}
                            onBlur={() => {
                              if (
                                hasValueChanged(
                                  dayjsFormat(contactDetails?.firstPurchase, 'YYYY-MM-DD'),
                                  values?.firstPurchase
                                )
                              )
                                handleInputBlurValue(
                                  {
                                    firstPurchase: new Date(values?.firstPurchase)?.toISOString(),
                                  },
                                  `changed First Purchase from ${
                                    dayjsFormat(contactDetails?.firstPurchase, 'MM/DD/YYYY') || 'None'
                                  } to ${dayjsFormat(values?.firstPurchase, 'MM/DD/YYYY')}`
                                )
                            }}
                          />

                          <SharedStyled.Text variant="link" fontWeight="700" fontSize="18px" margin="10px auto 0 0">
                            {' '}
                            <span onClick={() => toggleCount('TeamMembers')}>
                              {!toggleHeading[`TeamMembers`] ? <>&#9654;</> : <>&#9660;</>}
                              &nbsp; Team Members
                            </span>
                          </SharedStyled.Text>

                          {toggleHeading['TeamMembers'] ? (
                            <TeamMembers
                              values={values}
                              errors={errors}
                              touched={touched}
                              salesPersonDrop={salesPersonDrop}
                              officeDrop={officeDrop}
                              projectManagerDrop={projectManagerDrop}
                              setFieldValue={setFieldValue}
                              handleInputBlurValue={handleInputBlurValue}
                              contactDetails={contactDetails}
                            />
                          ) : null}

                          <SharedStyled.Text variant="link" fontWeight="700" fontSize="18px" margin="10px auto 0 0">
                            {' '}
                            <span onClick={() => toggleCount('Actions')}>
                              {!toggleHeading[`Actions`] ? <>&#9654;</> : <>&#9660;</>}
                              &nbsp; Actions
                            </span>
                          </SharedStyled.Text>
                          {toggleHeading['Actions'] ? (
                            <Actions values={values} errors={errors} setFieldValue={setFieldValue} touched={touched} />
                          ) : null}

                          <SharedStyled.ButtonContainer marginTop="10px" justifyContent="flex-start">
                            {isDeleted === 'false' && !opportunityData?.length && (
                              <Button
                                type="button"
                                className="delete"
                                width="max-content"
                                isLoading={deleteLoading}
                                onClick={() => deleteContactFunc()}
                              >
                                Make Inactive
                              </Button>
                            )}
                            {isDeleted === 'true' && (
                              <>
                                <Button
                                  type="button"
                                  width="max-content"
                                  bgColor={colors.green}
                                  onClick={() => restoreContactFunc()}
                                  isLoading={unDeleteLoading}
                                >
                                  Un-Delete Contact
                                </Button>
                                <Button
                                  type="button"
                                  width="max-content"
                                  className="delete"
                                  onClick={() => permanentDeleteContactFunc()}
                                  isLoading={permanentdeleteLoading}
                                >
                                  Permanently Delete Contact
                                </Button>
                              </>
                            )}
                          </SharedStyled.ButtonContainer>
                        </SharedStyled.Content>
                      </Form>
                    </LoadScript>
                  </Styled.ClientProfileContainer>

                  <div className="todo-comments">
                    <ToDoNextProfile
                      isContact
                      contactData={values}
                      contactOrOppId={contactId}
                      // initFetch={initFetch}
                      setContactData={setInitialValues}
                      fetchActivity={fetchActivity}
                    />

                    {leadStatus && (
                      <>
                        <br />
                        <LeadStage
                          stages={stages}
                          initFetchContact={getContactDetails}
                          fetchActivity={fetchActivity}
                          leads={leads}
                        />
                      </>
                    )}

                    <SharedStyled.FlexCol gap="10px">
                      <>
                        <SharedStyled.Text variant="link" fontWeight="700" fontSize="18px" margin="10px auto 0 0">
                          {' '}
                          <span onClick={toggleAllIndexes}>
                            {!Object.values(toggleHeading).every((val) => val === true) ? <>&#9654;</> : <>&#9660;</>}
                            &nbsp; Lead List
                          </span>
                        </SharedStyled.Text>

                        {leads?.map((lead: any, index: number) => (
                          <LeadInfo
                            key={lead._id}
                            leadIndex={index}
                            lead={lead}
                            leadSrcData={leadSrcData}
                            refererres={refererres}
                            projectTypesDrop={projectTypesDrop}
                            toggleCount={toggleCount}
                            toggleHeading={toggleHeading}
                            leads={leads || []}
                            setShowCreateRuleDialog={setShowCreateRuleDialog}
                            setTrackingRuleId={setTrackingRuleId}
                            setSelectedLeadId={setSelectedLeadId}
                            initFetch={() => {
                              fetchContactLeads()
                            }}
                            renderInvalidButton={
                              <>
                                {!isLost && isInvalid && (
                                  <Button
                                    disabled={!leads[0]?._id}
                                    type="button"
                                    width="max-content"
                                    className={'success'}
                                    onClick={handleInvalidLeadClick}
                                  >
                                    {'Valid Lead'}
                                  </Button>
                                )}
                              </>
                            }
                            renderLostButton={
                              <>
                                {!isInvalid && isLost && (
                                  <Button
                                    type="button"
                                    disabled={!leads[0]?._id}
                                    width="max-content"
                                    className={'success'}
                                    onClick={makeLost}
                                  >
                                    {'Un-Lose'} Lead
                                  </Button>
                                )}
                              </>
                            }
                          />
                        ))}
                      </>

                      <>
                        {/* <SharedStyled.Text variant="link" fontWeight="700" fontSize="18px" margin="10px auto 0 0">
                          {' '}
                          <span onClick={() => toggleCount('Tracking')}>
                            {!toggleHeading[`Tracking`] ? <>&#9654;</> : <>&#9660;</>}
                            &nbsp; Tracking
                          </span>
                        </SharedStyled.Text> */}

                        {/* {toggleHeading['Tracking'] ? <Tracking tracking={values.tracking} /> : null} */}
                      </>
                    </SharedStyled.FlexCol>

                    <br />
                    <ErrorBoundary>
                      <CommentsProfile comments={values?.comments || []} initFetchContact={getContactDetails} />
                    </ErrorBoundary>
                    <br />
                    <Activity getAtivity={getAtivity || []} activityLoading={activityLoading} />
                  </div>
                </Styled.ContactContainer>

                <Styled.OpportunityContainer marginTop="50px" width="100%">
                  <SharedStyled.FlexBox width="100%" alignItems="flex-start" gap="5px">
                    <SharedStyled.SectionTitle>Opportunities</SharedStyled.SectionTitle>
                  </SharedStyled.FlexBox>
                  <Table
                    columns={columns}
                    data={opportunityData}
                    loading={tableLoading}
                    // pageCount={pageCount}
                    fetchData={() => {}}
                    noSearch={true}
                    onRowClick={(val) => {
                      navigate(`/${getEnumValue(val?.stage?.stageGroup)}/opportunity/${val?._id}`)
                    }}
                  />
                </Styled.OpportunityContainer>
              </>
            )
          }}
        </Formik>
      )}

      <CustomModal show={showAddCityModal}>
        <AddCityModal setShowAddCityModal={setShowAddCityModal} action="Add City" setDetailsUpdate={setDataUpdate} />
      </CustomModal>

      <CustomModal show={referrerModal}>
        <ReferrerModal
          onClose={() => {
            setShowReferrerModal(false)
          }}
          onComplete={() => {
            initFetchReferrers()
          }}
        />
      </CustomModal>

      <CustomModal show={lostModal}>
        <LostLeadModal
          onClose={() => setLostModal(false)}
          onComplete={() => {
            setLostModal(false)
            fetchContactLeads()
          }}
          isLost={isLost}
          setIsLost={setIsLost}
          // initFetchContact={getContactDetails}
          leadId={leads[0]?._id}
        />
      </CustomModal>

      <CustomModal show={invalidLeadModal}>
        <InvalidLeadModal
          onClose={() => {
            setInvalidLeadModal(false)
          }}
          onComplete={() => {
            setLostModal(false)
            fetchContactLeads()
          }}
          fetchLinkedContact={fetchLinkedContact}
          fetchSearchContact={fetchSearchContact}
          isInvalid={isInvalid}
          setIsInvalid={setIsInvalid}
          // fetchActivity={fetchActivity}
          // initFetchContact={getContactDetails}
          leadId={leads[0]?._id}
        />
      </CustomModal>

      <CustomModal show={infoWarrentyModal}>
        <InfoWarrantyModal
          onClose={() => {
            setInfoWarrentyModal(false)
            setWarrentyModal(true)
          }}
          onConfirm={async () => {
            setInfoWarrentyModal(false)
            setWarrentyModal(true)
            await markLeadAsInvalid(
              {
                invalidLeadReason: 'Warranty',
              },
              leads[0]?._id
            )
          }}
          leadSource={getLeadSrcDropdownName(contactDetails?.leadSourceId, leadSrcData)?.sourceName || ''}
          campaign={getLeadSrcDropdownName(contactDetails?.campaignId, leadSrcData)?.sourceName || ''}
          daysAgo={daysSince(new Date(leads?.[0]?.newLeadDate).getTime()) || '--'}
          currentType={'New Lead'}
          newType={'Warranty'}
        />
      </CustomModal>

      {/* <CustomModal show={infoOpportunityModal}>
        <InfoOpportunityModal
          onClose={() => {
            setInfoOpportunityModal(false)
            setShowAddModal(true)
            setCreateNew(false)
            setUseExistingLead(true)
          }}
          onConfirm={async () => {
            setInfoOpportunityModal(false)
            setShowAddModal(true)
            setCreateNew(true)
            setUseExistingLead(false)
          }}
          leadSource={getLeadSrcDropdownName(leads?.[0]?.leadSourceId, leadSrcData)?.sourceName || ''}
          campaign={getLeadSrcDropdownName(leads?.[0]?.campaignId, leadSrcData)?.sourceName || ''}
          daysAgo={daysSince(new Date(leads?.[0]?.newLeadDate).getTime()) || '--'}
          currentType={'New Lead'}
        />
      </CustomModal> */}

      <CustomModal show={warrentyModal}>
        <AddLeadWarrantyModal
          onClose={() => {
            setWarrentyModal(false)
            setClientAutoFill({})
            setClientData({})
          }}
          onComplete={() => {
            setWarrentyModal(false)
            initFetch()
            setClientAutoFill({})
            setClientData({})
          }}
          setClientName={setClientName}
          clientData={clientData}
          setClientData={setClientData}
          setShowEditClientModal={setShowEditClientModal}
          contactWarranty
          clientInfo={{
            _id: contactDetails?._id,
            phone: contactDetails?.phone,
            email: contactDetails?.email,
            firstName: contactDetails?.firstName,
            lastName: contactDetails?.lastName,
            isBusiness: contactDetails?.isBusiness,
            businessName: contactDetails?.businessName,
            fullName: contactDetails?.isBusiness ? contactDetails?.businessName : contactDetails?.fullName,
            street: contactDetails?.street,
            city: contactDetails?.city,
            state: contactDetails?.state,
            zip: contactDetails?.zip,
            projectManager:
              getValueByKeyAndMatch('name', contactDetails?.projectManagerId || '', '_id', projectManagerDrop) || '',
          }}
          noLoadScript
        />
      </CustomModal>

      <CustomModal show={showAddModal}>
        <AddOpportunityModal
          onClose={() => {
            setShowAddModal(false)
            setClientAutoFill({})
            setClientData({})
          }}
          onComplete={() => {
            setShowAddModal(false)
            setClientAutoFill({})
            setClientData({})
            setCreatedClient({})
            navigate(`/sales`)
          }}
          setReferrerValue={setReferrerValue}
          refererres={refererres}
          clientAutoFill={clientAutoFill}
          setClientData={setClientData}
          clientData={{}}
          detailsUpdate={false}
          createdClient={{}}
          setShowAddNewClientModal={setShowAddNewClientModal}
          // setShowEditClientModal={setShowEditClientModal}
          isLead={true}
          activeLead={leads?.find((v) => v.status === 'active') ? true : false}
          leadInfo={{
            contactId: contactDetails?._id,
            fullName: contactDetails?.isBusiness ? contactDetails?.businessName : contactDetails?.fullName,
            isBusiness: contactDetails?.isBusiness,
            businessName: contactDetails?.businessName,
            firstName: contactDetails?.firstName,
            lastName: contactDetails?.lastName || '',
            phone: contactDetails?.phone,
            email: contactDetails?.email,
            notes: contactDetails?.notes,
            newLeadDate: contactDetails?.newLeadDate,
            leadSourceName:
              getLeadSrcDropdownName(contactDetails?.campaignId || contactDetails?.leadSourceId, leadSrcData)
                ?.sourceName || '',

            CSRAssigned: getValueByKeyAndMatch('name', contactDetails?.csrId, '_id', officeDrop) || '',
            referredBy: localReferrerName || '',
            // referredById: contactDetails?.referredBy,
            // leadSourceName: getLeadSrcDropdownName(
            //   contactDetails?.campaignId || contactDetails?.leadSourceId,
            //   leadSrcData
            // )?.sourceName,
            workTypeName:
              contactDetails?.workType === 'unknown'
                ? undefined
                : getValueByKeyAndMatch('name', contactDetails?.workType, 'id', projectTypesDrop),
            street: contactDetails?.street,
            city: contactDetails?.city,
            state: contactDetails?.state,
            zip: contactDetails?.zip,
            createNew: createNew ? true : leads?.[0]?.status === 'lost' ? true : false,
            leadId: leads?.[0]?._id || null,
          }}
        />
      </CustomModal>

      <CustomModal show={addNewClientModal}>
        <AddNewContactModal
          setShowAddNewClientModal={setShowAddNewClientModal}
          setDetailsUpdate={setDetailsUpdate}
          detailsUpdate={detailsUpdate}
          setClientAutoFill={setClientAutoFill}
          clientName={clientName}
          setCreatedClient={setCreatedClient}
          refererres={refererres}
          setReferrerValue={setReferrerValue}
          noLoadScript={true}
          isOpportunity
          setClientData={setClientData}
          setShowReferrerModal={setShowReferrerModal}
          // mergeContact={{ setContactDetails, setMatchingContacts, setMergeModal }}
        />
      </CustomModal>

      <CustomModal show={showCreateRuleDialog}>
        <CreateRuleDialog
          leadSrcData={leadSrcData}
          trackingData={trackingDataTag}
          trackingRuleId={trackingRuleId || undefined}
          selectedLeadId={selectedLeadId}
          isContact
          onComplete={async (data: any) => {
            if (hasValues(data)) {
              await updateLead(
                {
                  trackingRuleId: data?._id,
                  leadSourceId: data?.leadSourceId,
                  campaignId: data?.campaignId,
                },
                selectedLeadId
              )
            }

            setShowCreateRuleDialog(false)
            fetchContactLeads()
            setTrackingRuleId('')
          }}
          onClose={() => {
            setShowCreateRuleDialog(false)
            setTrackingRuleId('')
            setSelectedLeadId('')
          }}
        />
      </CustomModal>
    </>
  )
}

export default ContactProfile
