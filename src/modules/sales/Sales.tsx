import { <PERSON>, useNavigate } from 'react-router-dom'
import { useEffect, useState, useRef, useMemo } from 'react'
import { useSelector } from 'react-redux'

import * as SharedStyled from '../../styles/styled'
import * as Styled from './style'
import {
  extractPermissionByName,
  getDataFromLocalStorage,
  getKeysFromObjects,
  getPercentCompleteFromId,
  getSalesPersonIdFromName,
  isSuccess,
  nextAction,
  notify,
} from '../../shared/helpers/util'
import {
  getSalesOpportunity,
  getSalesOpportunityCompletion,
  getPositionMembersById,
  getStages,
  searchOpps,
} from '../../logic/apis/sales'
import useDebounce from '../../shared/hooks/useDebounce'
import { default as HorizontalScrollableDiv } from '../../shared/scrollableDiv/ScrollableDiv'

import { useClickOutside } from '../../shared/hooks/useClickOutside'
import { ButtonCont } from '../units/style'
import Button from '../../shared/components/button/Button'
import CustomSelect from '../../shared/customSelect/CustomSelect'
import { getPosition } from '../../logic/apis/position'
import { I_Opportunity } from '../opportunity/Opportunity'
import AddOpportunityModal, { I_Client } from './AddOpportunityModal'
import { CustomModal } from '../../shared/customModal/CustomModal'
import { useAppDispatch } from '../../logic/redux/reduxHook'
import { setFilterSaleBy } from '../../logic/redux/actions/ui'
import { getReferres } from '../../logic/apis/company'
import ReferrerModal from '../Refferer/components/referrerModal/ReferrerModal'
import { AddNewClientModal } from '../client/components/addNewClientModal/AddNewClientModal'
import { getClients } from '../../logic/apis/client'
import { colors } from '../../styles/theme'
import { RenderData, SLoader } from '../../shared/components/loader/Loader'
import { StageGroupEnum, StorageKey } from '../../shared/helpers/constants'
import { Stage } from '../crmSettings/CrmSettings'
import { I_Position, I_SalesPerson } from '../leads/AddNewLead'
import { AddNewContactModal } from '../contact/components/addNewContactModal/AddNewContactModal'
import { LoadScript } from '@react-google-maps/api'
import { getConfig } from '../../config'
import { EditContactModal } from '../contact/components/addNewContactModal/EditContactModal'
import ContactMergeModal from '../contact/components/contactProfile/components/ContactMergeModal'
import { createContact, migrateContact } from '../../logic/apis/contact'

export function getSortTime(column: any, item: any): number {
  return column?.sortField
    ? new Date(item?.checkpointActivity?.[column?.sortField]?.created)?.getTime()
    : new Date(item?.oppDate)?.getTime()
}

// Helper function to process stages
export const processStages = (stages: Stage[], groupStage: StageGroupEnum): Stage[] => {
  const newLeadStage = stages?.find((stage) =>
    groupStage === StageGroupEnum.Operations ? stage.code === 'preparePacket' : stage.code === 'opp'
  )

  const processContractStage = stages?.find((stage) =>
    groupStage === StageGroupEnum.Operations ? stage.name === 'Completed' : stage.code === 'processContract'
  )

  const filteredStages = stages?.filter((stage) =>
    groupStage === StageGroupEnum.Operations
      ? stage.code !== 'preparePacket' && stage.name !== 'Completed'
      : stage.code !== 'opp' && stage.code !== 'processContract'
  )

  return [newLeadStage, ...filteredStages, processContractStage].filter((stage) => stage !== undefined)
}

// Helper function to create board structure
export const createBoardStructure = (stages: Stage[], opportunities: any[]) => {
  // Create initial board structure
  const board = stages.map((stage: any) => ({
    title: stage.name,
    cards: [],
    id: stage.sequence,
    _id: stage._id,
    description: stage.description,
    sortField: stage?.sortingField ? Object.keys(stage?.sortingField)[0] : undefined,
    sortOrder: stage?.sortingField ? Object.values(stage?.sortingField)[0] : undefined,
  }))

  // Populate cards for each stage
  opportunities.forEach((opp) => {
    const stageColumn: any = board.find((column) => column._id === opp.stage)
    if (stageColumn) {
      stageColumn.cards.push({
        ...opp,
        title: `${opp?.contactName} - ${opp.num}`,
        // title: `${opp?.contactId?.firstName} ${opp?.contactId?.lastName ?? ''} - ${opp.num}`,
        id: stageColumn.cards.length + 1,
      })
    }
  })

  return board
}

const Sales = () => {
  const navigate = useNavigate()
  // const [shimmerLoading, setShimmerLoading] = useState(true)
  const [showAddModal, setShowAddModal] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [searchData, setSearchData] = useState([])
  const [searchedOpps, setSearchedOpps] = useState<I_Opportunity[]>([])
  const debouncedSearch = useDebounce(searchTerm, 500)
  const [referrerModal, setShowReferrerModal] = useState(false)
  const [refererres, setRefererres] = useState<any>([])
  const [referrerValue, setReferrerValue] = useState<any>([])
  const [addNewClientModal, setShowAddNewClientModal] = useState(false)
  const [editClientModal, setShowEditClientModal] = useState(false)
  const [detailsUpdate, setDetailsUpdate] = useState(false)
  const [clientAutoFill, setClientAutoFill] = useState<any>({})
  const [clientName, setClientName] = useState<any>('')
  const [clientDropdown, setClientDrop] = useState<I_Client[]>([])
  const [addressLoading, setAddressLoading] = useState<boolean>(true)
  const [createdClient, setCreatedClient] = useState({})
  const [searchLoading, setSearchLoading] = useState(false)
  const [clientData, setClientData] = useState<any>({})

  const [mergeModal, setMergeModal] = useState(false)
  const [matchingContacts, setMatchingContacts] = useState<any[]>([])
  const [contactDetails, setContactDetails] = useState<any>({})
  const addOpportunityVisibleTo = ['SalesPerson', 'RRTech']

  const initialBoard = {
    columns: [
      {
        id: 1,
        title: 'Backlog',
        cards: [],
      },
      {
        id: 2,
        title: 'Doing',
        cards: [],
      },
      {
        id: 3,
        title: 'Q&A',
        cards: [],
      },
      {
        id: 4,
        title: 'Production',
        cards: [],
      },
      {
        id: 5,
        title: 'Production2',
        cards: [],
      },
    ],
  }

  const [boardValue, setBoardValue] = useState(initialBoard)

  const globalSelector = useSelector((state: any) => state)
  const { currentMember, position, positionDetails, positionPermissions } = globalSelector.company
  const {
    filterSaleBy: { name: client, _id },
  } = globalSelector.ui
  // const [client, setClient] = useState('')

  const hasWritePermissionsForSales =
    Object.keys(positionPermissions)?.length &&
    extractPermissionByName(positionDetails, 'opportunity info')?.crud?.write

  const [opps, setOpps] = useState<I_Opportunity[]>([])
  const [stage, setStages] = useState<I_Opportunity[]>([])
  const [percentComplete, setPercentComplete] = useState<any>([])
  // const [salesPersonDrop, setSalesPersonDrop] = useState<I_SalesPerson[]>([])
  const [loading, setLoading] = useState(true)

  const [showDropdown, setShowDropdown] = useState<boolean>(false)
  const dropdownRef = useRef(null)
  const dispatch = useAppDispatch()
  useClickOutside(dropdownRef, setShowDropdown)

  const uniqueValues = useMemo(() => {
    const salesPerson = new Map<string, { name: string; _id: string }>()

    opps?.forEach((column) => {
      if (column?.salesPersonName) {
        salesPerson.set(column.salesPersonName, {
          name: column.salesPersonName,
          _id: column.salesPerson,
        })
      }
    })

    return {
      salesPerson: Array.from(salesPerson.values()),
    }
  }, [opps])

  const [filteredOpps, setFilteredOpps] = useState<{ columns: any[] }>({ columns: [] })

  useEffect(() => {
    if (_id && !loading && _id !== 'none') {
      const filter = boardValue.columns?.map((stage: any) => {
        return {
          ...stage,
          cards: stage?.cards?.filter((card: any) => {
            return card?.salesPerson === _id
          }),
        }
      })

      setFilteredOpps({ columns: filter })
    }
    if (client === 'None') {
      const availableSalesPerson = [...getKeysFromObjects(uniqueValues?.salesPerson, '_id')]
      console.log({ client, _id }, availableSalesPerson)

      const filter = boardValue.columns?.map((stage: any) => {
        return {
          ...stage,
          cards: stage?.cards?.filter((card: any) => {
            return !availableSalesPerson?.includes(card?.salesPerson)
          }),
        }
      })

      setFilteredOpps({ columns: filter })
    }
  }, [_id, loading])

  const StagesBoard = () => {
    const handleItemClick = (item: any) => {
      navigate(`/sales/opportunity/${item._id}`, {
        state: {
          isDeleted: false,
        },
      })
    }

    const renderData = filteredOpps?.columns?.length ? filteredOpps : boardValue

    const stageLoading = { columns: new Array(8).fill({ cards: new Array(14).fill('1') }) }

    return (
      <>
        {loading
          ? stageLoading.columns.map((column, idx) => {
              return (
                <Styled.StagesBoard key={idx} className="loading">
                  <h2>
                    <SLoader />
                  </h2>
                  <ul className="list-container">
                    {column.cards?.map((card: any, idx2: number) => (
                      <SLoader height={48} key={idx2} />
                    ))}
                  </ul>
                </Styled.StagesBoard>
              )
            })
          : renderData.columns.map((column, idx) => {
              const isDescending = column?.sortOrder === -1
              return (
                <Styled.StagesBoard key={column.id}>
                  {/* <h2 className="heading">{column.title}</h2> */}
                  <SharedStyled.FlexBox margin="10px 0 0 10px" gap="5px" alignItems="center">
                    <SharedStyled.TooltipContainer
                      width="180px"
                      positionLeft="0px"
                      positionBottom="0px"
                      positionLeftDecs="90px"
                      positionBottomDecs="unset"
                      // positionRightDecs="20px"
                      positionTopDecs="20px"
                      fontSize="14px"
                    >
                      <span className="tooltip-content">{column?.description || ''}</span>
                      <SharedStyled.Text fontSize="14px" fontWeight="600">
                        {column.title}{' '}
                        <SharedStyled.Text
                          fontSize="14px"
                          fontWeight="400"
                        >{`(${column?.cards?.length})`}</SharedStyled.Text>
                      </SharedStyled.Text>
                      {/* <SharedStyled.IButton>i</SharedStyled.IButton> */}
                    </SharedStyled.TooltipContainer>
                  </SharedStyled.FlexBox>
                  <ul className="list-container">
                    {column.cards
                      ?.sort((a: any, b: any) => {
                        const dateA = new Date(a?.createdAt)?.getTime()
                        const dateB = new Date(b?.createdAt)?.getTime()
                        return dateB - dateA
                      })
                      ?.sort((a: any, b: any) => {
                        const timeA = getSortTime(column, a)
                        const timeB = getSortTime(column, b)
                        return isDescending ? timeB - timeA : timeA - timeB
                      })
                      ?.map((card: any, idx2: number) => (
                        <Styled.ListItem
                          key={idx2}
                          percent={getPercentCompleteFromId(card._id, percentComplete)}
                          className="list-item"
                          onClick={() => handleItemClick(card)}
                          borderColor={nextAction(card)}
                        >
                          <h3>{card.title}</h3>

                          {card?.agingVal ? (
                            <SharedStyled.FlexCol alignItems="flex-end" className="aging-value">
                              <span>{card?.agingVal}d</span>
                            </SharedStyled.FlexCol>
                          ) : null}
                        </Styled.ListItem>
                      ))}
                  </ul>
                </Styled.StagesBoard>
              )
            })}
      </>
    )
  }
  useEffect(() => {
    initFetchReferrers()
  }, [])

  useEffect(() => {
    if (referrerValue === '--Add New--') {
      setShowReferrerModal(true)
    }
  }, [referrerValue])

  const initFetchReferrers = async () => {
    try {
      const res = await getReferres(false, false)
      if (isSuccess(res)) {
        const { referrers } = res?.data?.data
        const sortedReferrers = [...referrers].sort((a, b) => a.name.localeCompare(b.name))
        setRefererres(sortedReferrers)
      }
    } catch (error) {
      console.log(error)
    }
  }

  useEffect(() => {
    if (searchTerm !== '') {
      setShowDropdown(true)
    } else {
      setShowDropdown(false)
      setSearchData([])
    }
  }, [searchTerm])

  useEffect(() => {
    ;(async () => {
      if (debouncedSearch && opps?.length) {
        try {
          setSearchLoading(true)
          const res = await searchOpps({
            search: debouncedSearch,
            salesPerson:
              positionDetails.symbol === 'SalesPerson' || positionDetails.symbol == 'RRTech'
                ? positionDetails.memberId
                : _id,
            projectManager: positionDetails.symbol === 'ProjectManager' ? positionDetails.memberId : '',
          })

          setSearchData(res?.data?.data?.opps)
        } catch (error) {
          console.error('Search Error', error)
        } finally {
          setSearchLoading(false)
        }
      }
    })()
  }, [debouncedSearch, opps?.length])

  const initFetch = async () => {
    setLoading(true)

    const fetchParams = {
      deleted: false,
      salesPerson:
        positionDetails.symbol === 'SalesPerson' || positionDetails.symbol == 'RRTech' ? positionDetails.memberId : '',
      projectManager: positionDetails.symbol === 'ProjectManager' ? positionDetails.memberId : '',
      stageGroup: StageGroupEnum.Sales,
      // lost: false,
      status: 'active',
    }

    try {
      const [stagesRes, oppRes, completed] = await Promise.all([
        getStages({}, false, StageGroupEnum?.Sales),
        getSalesOpportunity(fetchParams),
        getSalesOpportunityCompletion(fetchParams),
      ])

      if (!isSuccess(stagesRes) || !isSuccess(oppRes)) {
        throw new Error(!isSuccess(stagesRes) ? stagesRes?.data.message : oppRes?.data.message)
      }

      const { stage: stages } = stagesRes.data.data
      const { completed: completePercent } = completed.data.data
      const { opportunity } = oppRes.data.data

      // Process stages
      const processedStages: any = processStages(stages, StageGroupEnum.Sales)
      setStages(processedStages)
      setPercentComplete(completePercent)

      const board = createBoardStructure(processedStages, opportunity)

      setOpps(opportunity)
      setBoardValue({ columns: board })
    } catch (error: any) {
      notify(error?.message ?? 'Unable to fetch sales data', 'error')
      console.error('getAllStages error:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (Object.entries(positionDetails)?.length > 0) {
      initFetch()
    }
  }, [positionDetails])

  const handleMergeConfirm = async ({ mergedFields, toContact }: any) => {
    try {
      const res = await migrateContact(toContact, mergedFields)
      if (isSuccess(res)) {
        notify('Contact Profile Merged Successfully', 'success')
        setClientData(matchingContacts[0])
        setCreatedClient(matchingContacts[0])
      }
    } catch (error) {
    } finally {
      setMergeModal(false)
    }
  }

  const handleMergeCancel = async () => {
    try {
      let response = await createContact(contactDetails)
      if (isSuccess(response)) {
        setClientData(contactDetails)
        setCreatedClient(contactDetails)
        notify('Contact Created Successfully', 'success')
      }
    } catch (error) {
      setClientData({})
      setCreatedClient({})
      console.log({ error })
    } finally {
      setMergeModal(false)
      setLoading(false)
    }
  }

  return (
    <>
      <Styled.SalesContainer>
        <SharedStyled.FlexRow justifyContent="space-between">
          <SharedStyled.SectionTitle className="opportunity">Sales</SharedStyled.SectionTitle>
        </SharedStyled.FlexRow>

        <SharedStyled.FlexRow margin="10px 0 0 0" justifyContent="space-between" className="filter">
          <SharedStyled.FlexRow className="filterCont">
            {
              // addOpportunityVisibleTo?.includes(positionDetails.symbol) // NHR-1567 point 1

              !hasWritePermissionsForSales ? (
                <></>
              ) : (
                <RenderData loader={<SLoader width={104} height={40} />} loading={loading}>
                  <ButtonCont>
                    <Button
                      onClick={() => {
                        // handleNewLeadClick()
                        setShowAddModal(true)
                      }}
                    >
                      New Opportunity
                    </Button>
                  </ButtonCont>
                </RenderData>
              )
            }

            <RenderData loader={<SLoader width={104} height={40} />} loading={loading}>
              {uniqueValues?.salesPerson?.length ? (
                <CustomSelect
                  value={client}
                  dropDownData={['Show All', ...uniqueValues?.salesPerson?.map((v) => v.name), 'None']}
                  stateName="client"
                  setValue={(val: string) => {
                    const id = val === 'None' ? 'none' : getSalesPersonIdFromName(val, uniqueValues?.salesPerson)
                    if (val === 'Show All') {
                      setFilteredOpps({ columns: [] })
                    }
                    dispatch(setFilterSaleBy({ _id: id, name: val }))
                  }}
                  innerHeight="40px"
                  className="sales"
                />
              ) : null}
            </RenderData>
          </SharedStyled.FlexRow>
        </SharedStyled.FlexRow>

        <Styled.BoardContainer marginTop="10px">
          <HorizontalScrollableDiv>
            <StagesBoard />
          </HorizontalScrollableDiv>
        </Styled.BoardContainer>
        <SharedStyled.FlexBox width="100%" gap="5px" wrap="wrap" justifyContent="flex-end">
          <Button
            padding="4px"
            type="button"
            maxWidth="200px"
            className="gray"
            height="35px"
            onClick={() => {
              navigate(`/sales/deleted`)
            }}
          >
            Deleted Opportunities
          </Button>
          <Button
            padding="4px"
            type="button"
            maxWidth="200px"
            height="35px"
            onClick={() => {
              navigate(`/sales/completed`)
            }}
          >
            {/* <Link to={`/sales/opportunities/inactive`} style={{ color: 'black' }}> */}
            Old Completed
            {/* </Link> */}
          </Button>
          <Button
            padding="4px"
            type="button"
            maxWidth="200px"
            bgColor={colors.yellow}
            height="35px"
            onClick={() => {
              navigate(`/sales/opportunities/inactive`)
            }}
          >
            {/* <Link to={`/sales/opportunities/inactive`} style={{ color: 'black' }}> */}
            Inactive Opportunities
            {/* </Link> */}
          </Button>
          <Button
            padding="4px"
            type="button"
            maxWidth="200px"
            bgColor={colors.errorRed}
            height="35px"
            onClick={() => {
              navigate(`/sales/opportunities/lost`)
            }}
          >
            {/* <Link to={`/sales/opportunities/lost`} style={{ color: 'white' }}> */}
            Lost Opportunities
            {/* </Link> */}
          </Button>
        </SharedStyled.FlexBox>

        <CustomModal show={showAddModal}>
          <LoadScript
            googleMapsApiKey={getConfig().googleAddressApiKey}
            //  @ts-ignore
            libraries={['places']}
            loadingElement={<SLoader height={35} width={100} isPercent />}
          >
            <AddOpportunityModal
              onClose={() => {
                setShowAddModal(false)
                setClientAutoFill({})
                setClientData({})
              }}
              onComplete={() => {
                setShowAddModal(false)
                setClientAutoFill({})
                setClientData({})
                setCreatedClient({})
                initFetch()
              }}
              setReferrerValue={setReferrerValue}
              refererres={refererres}
              clientAutoFill={clientAutoFill}
              setClientData={setClientData}
              clientData={clientData}
              setShowAddNewClientModal={setShowAddNewClientModal}
              setShowEditClientModal={setShowEditClientModal}
              detailsUpdate={detailsUpdate}
              setClientName={setClientName}
              createdClient={createdClient}
              setShowReferrerModal={setShowReferrerModal}
            />
          </LoadScript>
        </CustomModal>
        {/* </LoadScript> */}

        <CustomModal show={editClientModal}>
          <EditContactModal
            setShowEditClientModal={setShowEditClientModal}
            setClientAutoFill={setClientAutoFill}
            clientName={clientName}
            noLoadScript={true}
            clientData={clientData}
            setClientData={setClientData}
            setDetailsUpdate={setDetailsUpdate}
            detailsUpdate={detailsUpdate}
            setCreatedClient={setCreatedClient}
            refererres={refererres}
            initFetchReferrers={initFetchReferrers}
            setReferrerValue={setReferrerValue}
          />
        </CustomModal>

        <CustomModal show={addNewClientModal}>
          <AddNewContactModal
            setShowAddNewClientModal={setShowAddNewClientModal}
            setDetailsUpdate={setDetailsUpdate}
            detailsUpdate={detailsUpdate}
            setClientAutoFill={setClientAutoFill}
            clientName={clientName}
            setCreatedClient={setCreatedClient}
            refererres={refererres}
            setReferrerValue={setReferrerValue}
            noLoadScript={true}
            isOpportunity
            setClientData={setClientData}
            setShowReferrerModal={setShowReferrerModal}
            mergeContact={{ setContactDetails, setMatchingContacts, setMergeModal }}
          />
        </CustomModal>
        <CustomModal show={referrerModal}>
          <ReferrerModal
            onClose={() => {
              setShowReferrerModal(false)
              // setEditReferrerVals(null)
            }}
            onComplete={() => {
              initFetchReferrers()
            }}
          />
        </CustomModal>

        <CustomModal show={mergeModal}>
          <ContactMergeModal
            existingContacts={[contactDetails, ...matchingContacts.slice(1)]}
            newContactData={matchingContacts[0]}
            onClose={() => {
              handleMergeCancel()
            }}
            onMerge={handleMergeConfirm}
            isNewContact={true}
          />
        </CustomModal>
      </Styled.SalesContainer>
    </>
  )
}

export default Sales
